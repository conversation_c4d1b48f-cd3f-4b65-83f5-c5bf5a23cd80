import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/db/prisma.service';
import { firstValueFrom } from 'rxjs';
import * as CryptoJS from 'crypto-js';

@Injectable()
export class TediyeService {
  private readonly logger = new Logger(TediyeService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async createTediye(): Promise<any> {
    try {
      // Günün başlangıcı ve bitişi
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() + 1,
      );

      // Günün payments verilerini al
      const payments = await this.prisma.$queryRaw`
        SELECT
          p.id,
          p.sale_uuid,
          p.payment_method,
          p.amount,
          p.refunded,
          p.cek_no,
          p.created_at,
          rs.market_id,
          rs.receipt_number,
          rs.customer,
          rs.carikod
        FROM payments p
        INNER JOIN sale_refunds rs ON rs.uuid = p.sale_uuid
        WHERE p.deleted_at IS NULL
        AND rs.deleted_at IS NULL
        AND p.created_at >= ${startOfDay}
        AND p.created_at < ${endOfDay}
        ORDER BY p.created_at DESC
      `;

      if (!payments || (payments as any[]).length === 0) {
        return {
          success: false,
          message: 'Bugün için ödeme verisi bulunamadı',
          data: {
            paymentsCount: 0,
          },
        };
      }

      // Verileri MikroApi formatına dönüştür
      const tediyeData = this.transformToTediyeFormat(payments as any[]);

      // MIKRO_API'ye POST isteği at
      const apiUrl = `http://${process.env.MIKRO_API}/api/MikroApi/tediyeCreate`;

      this.logger.log(`Sending data to MIKRO_API: ${apiUrl}`);
      this.logger.log(`Data: ${JSON.stringify(tediyeData, null, 2)}`);

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, tediyeData, {
          headers: {
            'Content-Type': 'application/json',
          },
        }),
      );

      this.logger.log(`MIKRO_API Response: ${JSON.stringify(response.data)}`);

      return {
        success: true,
        message: 'Tediye başarıyla oluşturuldu',
        data: {
          paymentsCount: (payments as any[]).length,
          mikroApiResponse: response.data,
        },
      };
    } catch (error) {
      this.logger.error('Tediye oluşturulurken hata:', error);
      this.logger.error('Error stack:', error.stack);

      // Axios error ise response detaylarını logla
      if (error.response) {
        this.logger.error('Error Response Status:', error.response.status);
        this.logger.error('Error Response Data:', error.response.data);
        this.logger.error('Error Response Headers:', error.response.headers);
      }

      return {
        success: false,
        message: 'Tediye oluşturulurken hata oluştu',
        error: error.message,
        details: error.response?.data || null,
      };
    }
  }

  private transformToTediyeFormat(payments: any[]): any {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const formattedDate = `${year}-${month}-${day}`;

    // MD5 hash için tarih + şifre
    const stringToHash = `${formattedDate} 159753`;
    const md5Hash = CryptoJS.MD5(stringToHash).toString();

    // Evrakları oluştur
    const evraklar: any[] = [];

    // Payments'ları market_id'ye göre grupla
    const paymentsByMarket = payments.reduce((acc, payment) => {
      const marketId = payment.market_id || '101';
      if (!acc[marketId]) {
        acc[marketId] = [];
      }
      acc[marketId].push(payment);
      return acc;
    }, {});

    // Her market için evrak oluştur
    Object.keys(paymentsByMarket).forEach(marketId => {
      const marketPayments = paymentsByMarket[marketId];

      // Satirları oluştur
      const satirlar = marketPayments.map((payment: any) => {
        // Ödeme metoduna göre kasa kodu belirle
        let kasaKodu = '0002'; // Default: Nakit
        if (
          payment.payment_method === 'CREDIT_CARD' ||
          payment.payment_method === 'DEBIT_CARD'
        ) {
          kasaKodu = '0008'; // POS/Kredi Kartı
        }

        return {
          cha_tarihi: new Date().toLocaleDateString('tr-TR'), // Bugünün tarihi
          cha_tip: 0,
          cha_cinsi: 0,
          cha_normal_Iade: 0,
          cha_evrak_tip: 64,
          cha_evrakno_seri: `PST${marketId}`,
          cha_cari_cins: 0,
          cha_kod: payment.carikod || '120.P001', // Müşteri kodu varsa onu kullan, yoksa perakende
          cha_d_kurtar: null,
          cha_d_cins: 0,
          cha_d_kur: 1,
          cha_srmrkkodu: marketId, // Şube kodu
          cha_projekodu: '',
          cha_kasa_hizmet: 4,
          cha_kasa_hizkod: kasaKodu,
          cha_vade: this.formatDateToYYYYMMDD(new Date()), // Bugünün tarihi
          cha_meblag: payment.amount.toString(),
          cha_aciklama: `${marketId} Şubenin ${kasaKodu === '0002' ? 'Nakit' : 'POS'} Kasa Tahsilatı`,
          user_tablo: [
            {
              TransactionReferenceId: payment.sale_uuid,
            },
          ],
        };
      });

      evraklar.push({
        evrak_aciklamalari: [
          {
            aciklama: `${marketId} Şubenin Kasa Tahsilatı`,
          },
        ],
        satirlar: satirlar,
      });
    });

    return {
      Mikro: {
        FirmaKodu: 'SOPHIGET',
        CalismaYili: '2025',
        KullaniciKodu: 'API',
        Sifre: md5Hash,
        FirmaNo: 0,
        SubeNo: 0,
        ApiKey:
          '+l0/ibuYEszgdtALHaUByLuvUx6CCdpHbDEPwzh+5uP2rH9jVnESFRk1MPbcOc7F17N0jdfdrXtgtzPgEkSZqK7txJhjXSGlup95MdUqNIA=',
        evraklar: evraklar,
      },
    };
  }

  private formatDateToDDMMYYYY(date: Date | string): string {
    const dateObj = new Date(date);
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    return `${day}.${month}.${year}`;
  }

  private formatDateToYYYYMMDD(date: Date | string): string {
    const dateObj = new Date(date);
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    return `${year}${month}${day}`;
  }
}
