import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from 'src/db/prisma.service';
import { CreateGiderPusulasiRequestDto } from './dto/create-giderpusulasi.dto';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class GiderpusulasiService {
  private readonly logger = new Logger(GiderpusulasiService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {}

  async createGiderPusulasi(): Promise<any> {
    try {
      // Günün başlangıcı ve bitişi
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const endOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate() + 1,
      );

      // Günün sale_refunds verilerini al
      const saleRefunds = await this.prisma.$queryRaw`
        SELECT
          sr.id,
          sr.uuid,
          sr.total_refund_amount,
          sr.initiated_by,
          sr.received_at,
          sr.receipt_number,
          sr.market_id
        FROM sale_refunds sr
        WHERE sr.deleted_at IS NULL
        AND sr.received_at >= ${startOfDay}
        AND sr.received_at < ${endOfDay}
        ORDER BY sr.received_at DESC
      `;

      // Günün sale_refund_items verilerini al
      const saleRefundItems = await this.prisma.$queryRaw`
        SELECT
          sri.id,
          sri.refund_uuid,
          sri.inventory_code,
          sri.inventory_name,
          sri.item_refund_reason,
          sri.payment_method,
          sri.quantity,
          sri.weight,
          sri.weight_unit,
          sri.unit_price,
          sri.total_price
        FROM sale_refund_items sri
        INNER JOIN sale_refunds sr ON sr.uuid = sri.refund_uuid
        WHERE sri.deleted_at IS NULL
        AND sr.deleted_at IS NULL
        AND sr.received_at >= ${startOfDay}
        AND sr.received_at < ${endOfDay}
      `;

      // Verileri DTO formatına dönüştür
      const evraklar = this.transformToGiderPusulasiDto(
        saleRefunds as any[],
        saleRefundItems as any[],
      );

      // MikroApi formatına dönüştür
      const giderPusulasiData = {
        Mikro: {
          FirmaKodu: 'SOPHIGET',
          CalismaYili: '2025',
          KullaniciKodu: 'API',
          Sifre: '9fce5c5935a265b4e6f54754c25158fd',
          FirmaNo: 0,
          subeno: 0,
          ApiKey:
            '+l0/ibuYEszgdtALHaUByLuvUx6CCdpHbDEPwzh+5uP2rH9jVnESFRk1MPbcOc7F17N0jdfdrXtgtzPgEkSZqK7txJhjXSGlup95MdUqNIA=',
          evraklar: evraklar.evraklar,
        },
      };

      // MIKRO_API'ye POST isteği at
      const apiUrl = `http://${process.env.MIKRO_API}/api/MikroApi/giderpusulasiCreate`;

      this.logger.log(`Sending data to MIKRO_API: ${apiUrl}`);
      this.logger.log(`Data: ${JSON.stringify(giderPusulasiData, null, 2)}`);

      const response = await firstValueFrom(
        this.httpService.post(apiUrl, giderPusulasiData, {
          headers: {
            'Content-Type': 'application/json',
          },
        }),
      );

      this.logger.log(`MIKRO_API Response: ${JSON.stringify(response.data)}`);

      return {
        success: true,
        message: 'Gider pusulası başarıyla oluşturuldu',
        data: {
          refundsCount: (saleRefunds as any[]).length,
          itemsCount: (saleRefundItems as any[]).length,
          mikroApiResponse: response.data,
        },
      };
    } catch (error) {
      this.logger.error('Gider pusulası oluşturulurken hata:', error);
      this.logger.error('Error stack:', error.stack);

      // Axios error ise response detaylarını logla
      if (error.response) {
        this.logger.error('Error Response Status:', error.response.status);
        this.logger.error('Error Response Data:', error.response.data);
        this.logger.error('Error Response Headers:', error.response.headers);
      }

      return {
        success: false,
        message: 'Gider pusulası oluşturulurken hata oluştu',
        error: error.message,
        details: error.response?.data || null,
      };
    }
  }

  private transformToGiderPusulasiDto(
    saleRefunds: any[],
    saleRefundItems: any[],
  ): CreateGiderPusulasiRequestDto {
    // Sale refund items'ları refund_uuid'ye göre grupla
    const itemsByRefundUuid = saleRefundItems.reduce((acc, item) => {
      if (!acc[item.refund_uuid]) {
        acc[item.refund_uuid] = [];
      }
      acc[item.refund_uuid].push(item);
      return acc;
    }, {});

    const evraklar = saleRefunds.map((refund, index) => {
      const refundItems = itemsByRefundUuid[refund.uuid] || [];
      const today = new Date().toLocaleDateString('tr-TR');
      const texPercent = 0;
      // inventory_code a göre taxPercent hesapla

      // Her refund için detay oluştur
      const detay = refundItems.map((item: any) => ({
        sth_tarih: today,
        sth_tip: '0',
        sth_cins: 1,
        sth_normal_iade: 1,
        sth_evraktip: '3',
        sth_evrakno_seri: 'GPS' + refund.market_id,
        sth_evrakno_sira: index + 1,
        sth_belge_no: refund.receipt_number,
        sth_belge_tarih: today,
        sth_stok_kod: item.inventory_code,
        sth_pos_satis: 1,
        sth_cari_cinsi: '0',
        sth_cari_kodu: '120.P001',
        sth_har_doviz_kuru: 1,
        sth_stok_doviz_kuru: 1,
        sth_miktar: item.quantity,
        sth_birim_pntr: 1,
        sth_tutar: item.total_price - item.total_price * taxPercent,
        sth_iskonto1: 0,
        sth_vergi_pntr: 2,
        sth_vergi: item.total_price * item.tax_percent, // %1 vergi varsayımı
        sth_aciklama: `REFUND: ${refund.uuid}`,
        sth_giris_depo_no: item.market_id,
        sth_cikis_depo_no: refund.market_id,
        sth_cari_srm_merkezi: refund.market_id,
        sth_stok_srm_merkezi: refund.market_id,
        sth_vergisiz_fl: false,
        sth_adres_no: 1,
        sth_fiyat_liste_no: 1,
        sth_satis_fiyat_doviz_kuru: 1,
      }));

      return {
        cha_evrak_tip: '0',
        cha_evrakno_seri: 'GPS' + refund.market_id,
        cha_evrakno_sira: index + 1,
        cha_satir_no: 0,
        cha_tarihi: today,
        cha_tip: '1',
        cha_cinsi: '7',
        cha_normal_Iade: 1,
        cha_tpoz: '0',
        cha_belge_no: refund.receipt_number,
        cha_belge_tarih: today,
        cha_aciklama: `REFUND: ${refund.uuid}`,
        cha_cari_cins: '0',
        cha_kod: '120.P001',
        cha_ciro_cari_kodu: '120.P001',
        cha_d_kur: '1',
        cha_srmrkkodu: refund.market_id,
        cha_karsid_kur: 1,
        cha_miktari: '0',
        cha_meblag: refund.total_refund_amount,
        cha_aratoplam: refund.total_refund_amount,
        cha_ft_iskonto1: 0,
        cha_vergipntr: 0,
        cha_pos_hareketi: 1,
        cha_adres_no: 1,
        cha_vergi2: refund.total_refund_amount, // %1 vergi varsayımı
        cha_fatura_belge_turu: 2,
        cha_diger_belge_adi: 'Gider Pusulası',
        detay,
        evrak_aciklamalari: [
          {
            aciklama: `API tarafından gönderildi - ${today}`,
          },
        ],
        user_tablo: [
          {
            CreditReferenceNumber: '3',
            CreditRelationCustomer: '2',
            DetailDescription1: '9',
            DetailDescription2: '10',
            RegisteredEMailAccount: '5',
            RentalCustomer: '7',
            SubDealer: '1',
            WebSupportCustomer: '4',
            WebSupportStartDate: today,
          },
        ],
      };
    });

    return { evraklar };
  }
}
