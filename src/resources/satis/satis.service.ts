import { Injectable, Logger } from '@nestjs/common';
import { MssqlService2 } from '../mssql2/mssql2.service';
import { PrismaService } from 'src/db/prisma.service';
import { BugsinkService } from '../../common/bugsink/bugsink.service';

import * as CryptoJS from 'crypto-js';
import * as fs from 'fs';
import * as path from 'path';

@Injectable()
export class SatisService {
  private readonly logger = new Logger(SatisService.name);

  constructor(
    private readonly mssqlService: MssqlService2,
    private readonly prisma: PrismaService,
    private readonly bugsinkService: BugsinkService,
  ) {}

  private savePostDataToFile(data: any, filename: string): void {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const fullFilename = `${timestamp}_${filename}.json`;
      const filePath = path.join(process.cwd(), 'logs', fullFilename);

      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      const jsonData = JSON.stringify(data, null, 2);
      fs.writeFileSync(filePath, jsonData, 'utf8');

      this.logger.log(`[POST-DATA-SAVE] Veri kaydedildi: ${filePath}`);
    } catch (error) {
      this.logger.error('[POST-DATA-SAVE] Dosya kaydetme hatası:', error);
    }
  }

  private formatDateToDDMMYYYY(date: Date | string | null | undefined): string {
    if (!date) return '';

    let dateObj: Date;

    if (typeof date === 'string') {
      dateObj = new Date(date);
    } else if (date instanceof Date) {
      dateObj = date;
    } else {
      return '';
    }

    if (isNaN(dateObj.getTime())) {
      return '';
    }

    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();

    return `${day}.${month}.${year}`;
  }

  async stokHareketInsertApi(requestBody?: any) {
    try {
      this.logger.log('[SATIS-API] stokHareketInsert processing started');

      if (requestBody) {
        this.savePostDataToFile(requestBody, 'satis_stokHareketInsert_request');
      }

      const sales = await this.mssqlService.getTodaySales();
      const salesItems = await this.mssqlService.getTodaysSaleItems();

      if (!sales.data || sales.data.length === 0) {
        return {
          success: false,
          message: 'Bugün için satış verisi bulunamadı',
          timestamp: new Date().toISOString(),
        };
      }

      const evraklar: any[] = [];

      for (const sale of sales.data) {
        const saleItemsOfSale = salesItems.filter(
          i => i.sale_uuid === sale.uuid,
        );
        if (!saleItemsOfSale.length) continue;

        let matrahKod1 = 0;
        let matrahKod2 = 0;
        let matrahKod3 = 0;
        let matrahKod4 = 0;

        const detaylar = await Promise.all(
          saleItemsOfSale.map(async item => {
            const quantity = Number(item.quantity);
            const unitPrice = Number(item.unit_price);
            const tutar = Number((quantity * unitPrice).toFixed(2));

            let kdvKodu = item.perakende_vergi_kodu;
            let kdvOrani = item.perakende_vergi_orani;

            if (!kdvKodu || kdvKodu === 0 || !kdvOrani || kdvOrani === 0) {
              const rec = await this.prisma.inventory.findFirst({
                where: { inventory_code: item.inventory_code },
                select: {
                  perakende_vergi_kodu: true,
                  perakende_vergi_orani: true,
                },
              });
              kdvKodu = kdvKodu || (rec?.perakende_vergi_kodu ?? 0);
              kdvOrani = kdvOrani || (rec?.perakende_vergi_orani ?? 0);
            }

            const kdvMatrah = Number(item.kdv_matrah ?? 0);

            console.log('item imte imte', item);

            switch (kdvKodu) {
              case 1:
                matrahKod1 += kdvMatrah;
                break;
              case 2:
                matrahKod2 += kdvMatrah;
                break;
              case 3:
                matrahKod3 += kdvMatrah;
                break;
              case 4:
                matrahKod4 += kdvMatrah;
                break;
            }

            return {
              sth_tarih: this.formatDateToDDMMYYYY(sale.created_at),
              sth_tip: '1',
              sth_cins: '0',
              sth_normal_iade: '0',
              sth_evraktip: '4',
              sth_evrakno_seri: `PSF101`,
              sth_evrakno_sira: await this.mssqlService.getNextSequentialValue(
                'CARI_HESAP_HAREKETLERI',
                'cha_evrakno_sira',
              ),
              sth_belge_no: '',
              sth_belge_tarih: this.formatDateToDDMMYYYY(sale.created_at),
              sth_stok_kod: item.inventory_code,
              sth_pos_satis: 1,
              sth_cari_cinsi: '0',
              sth_cari_kodu: sale.carikod ?? '120.P001',
              sth_har_doviz_kuru: 1,
              sth_stok_doviz_kuru: 1,
              sth_miktar: quantity,
              sth_birim_pntr: 1,
              sth_tutar: tutar - kdvMatrah,
              sth_iskonto1: item.discount ?? 0,
              sth_iskonto2: 0,
              sth_iskonto3: 0,
              sth_iskonto4: 0,
              sth_iskonto5: 0,
              sth_iskonto6: 0,
              sth_isk_mas1: 1,
              sth_isk_mas2: 1,
              sth_isk_mas3: 1,
              sth_isk_mas4: 1,
              sth_isk_mas5: 1,
              sth_isk_mas6: 1,
              sth_isk_mas7: 1,
              sth_isk_mas8: 1,
              sth_isk_mas9: 1,
              sth_isk_mas10: 1,
              sth_vergi_pntr: kdvKodu,
              sth_vergi: kdvMatrah,
              sth_aciklama: '',
              sth_giris_depo_no: '101',
              sth_cikis_depo_no: '101',
              sth_cari_srm_merkezi: '101',
              sth_stok_srm_merkezi: '101',
              sth_vergisiz_fl: false,
              sth_adres_no: 1,
              sth_fiyat_liste_no: 1,
              sth_satis_fiyat_doviz_kuru: 1,
            };
          }),
        );

        const evrak = {
          cha_evrak_tip: 63,
          cha_evrakno_seri: `PSF101`,
          cha_evrakno_sira: await this.mssqlService.getNextSequentialValue(
            'CARI_HESAP_HAREKETLERI',
            'cha_evrakno_sira',
          ),
          cha_tarihi: this.formatDateToDDMMYYYY(sale.created_at),
          cha_tip: 0,
          cha_cinsi: 7,
          cha_normal_Iade: 0,
          cha_tpoz: 0,
          cha_belge_no: '',
          cha_belge_tarih: this.formatDateToDDMMYYYY(sale.created_at),
          cha_aciklama: `Satış fişi - ${sale.receipt_number || sale.uuid}`,
          cha_cari_cins: 0,
          cha_kod: sale.carikod ?? '120.P001',
          cha_ciro_cari_kodu: sale.carikod ?? '120.P001',
          cha_d_kur: 1,
          cha_srmrkkodu: '101',
          cha_karsid_kur: 1,
          cha_miktari: '0',
          //cha_meblag: sale.total_price,
          cha_meblag:
            sale.total_price -
            (matrahKod1 + matrahKod2 + matrahKod3 + matrahKod4),
          cha_aratoplam: sale.total_price,
          cha_ft_iskonto1: sale.discount ?? 0,
          cha_ft_iskonto2: 0,
          cha_ft_iskonto3: 0,
          cha_ft_iskonto4: 0,
          cha_ft_iskonto5: 0,
          cha_ft_iskonto6: 0,
          cha_adres_no: 1,
          cha_vergi1: +matrahKod1.toFixed(2),
          cha_vergi2: +matrahKod2.toFixed(2),
          cha_vergi3: +matrahKod3.toFixed(2),
          cha_vergi4: +matrahKod4.toFixed(2),
          cha_vergi5: 0,
          cha_vergi6: 0,
          cha_vergi7: 0,
          cha_vergi8: 0,
          cha_vergi9: 0,
          cha_vergi10: 0,
          evrak_aciklamalari: [{ aciklama: 'Perakende satış fişi API' }],
          user_tablo: [
            {
              CreditReferenceNumber: '3',
              CreditRelationCustomer: '2',
              DetailDescription1: '9',
              DetailDescription2: '10',
              RegisteredEMailAccount: '5',
              RentalCustomer: '7',
              SubDealer: '1',
              WebSupportCustomer: '4',
              WebSupportStartDate: this.formatDateToDDMMYYYY(sale.created_at),
            },
          ],
          detay: detaylar,
        };

        evraklar.push(evrak);
      }

      const today = new Date();
      const year = today.getFullYear();
      const month = String(today.getMonth() + 1).padStart(2, '0');
      const day = String(today.getDate()).padStart(2, '0');
      const formattedDate = `${year}-${month}-${day}`;

      const stringToHash = `${formattedDate} 159753`;
      const md5Hash = CryptoJS.MD5(stringToHash).toString();

      const apiRequestBody = {
        Mikro: {
          FirmaKodu: 'SOPHIGET',
          CalismaYili: '2025',
          KullaniciKodu: 'API',
          Sifre: md5Hash,
          FirmaNo: 0,
          subeno: 0,
          ApiKey:
            '+l0/ibuYEszgdtALHaUByLuvUx6CCdpHbDEPwzh+5uP2rH9jVnESFRk1MPbcOc7F17N0jdfdrXtgtzPgEkSZqK7txJhjXSGlup95MdUqNIA=',
          evraklar: evraklar,
        },
      };

      this.savePostDataToFile(
        apiRequestBody,
        'satis_alim_satim_evragi_api_request',
      );

      const apiUrl = `http://${process.env.MIKRO_API}/api/MikroApi/alim-satim-evragi`;

      try {
        const response = await fetch(apiUrl, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(apiRequestBody),
        });

        const contentType = response.headers.get('content-type');
        let responseData: any;
        let responseText: any;

        if (contentType && contentType.includes('application/json')) {
          responseData = await response.json();
        } else {
          responseText = await response.text();
          this.logger.error(
            `[SATIS-API] API JSON dönmedi. Status: ${response.status}, Content-Type: ${contentType}`,
          );
          this.logger.error('Object:', responseText);

          if (contentType && contentType.includes('application/problem+json')) {
            try {
              const errorData = JSON.parse(responseText);
              this.logger.error(
                '[SATIS-API] Error Details:',
                JSON.stringify(errorData, null, 2),
              );
              return {
                success: false,
                message: 'MikroApi validation hatası',
                error: errorData,
                timestamp: new Date().toISOString(),
              };
            } catch (parseError) {
              this.logger.error('[SATIS-API] JSON parse hatası:', parseError);
            }
          }
          throw new Error(
            `API beklenen formatta yanıt vermedi. Status: ${response.status}, Content-Type: ${contentType}`,
          );
        }

        if (!response.ok) {
          throw new Error(
            `API hatası: ${response.status} - ${JSON.stringify(responseData)}`,
          );
        }

        this.savePostDataToFile(
          responseData,
          'satis_alim_satim_evragi_api_response',
        );

        return {
          success: true,
          message: `${evraklar.length} adet evrak başarıyla MikroApi'ye gönderildi`,
          processedCount: evraklar.length,
          apiResponse: responseData,
          timestamp: new Date().toISOString(),
        };
      } catch (apiError: any) {
        this.savePostDataToFile(
          {
            error: apiError.message,
            apiUrl,
            requestBody: apiRequestBody,
            timestamp: new Date().toISOString(),
          },
          'satis_alim_satim_evragi_api_error',
        );
        return {
          success: false,
          message: 'MikroApi isteği başarısız',
          error: apiError.message,
          apiUrl,
          requestBody: apiRequestBody,
          timestamp: new Date().toISOString(),
        };
      }
    } catch (error: any) {
      this.logger.error('[SATIS-API] Genel hata:', error);
      return {
        success: false,
        error: error.message,
        details: 'Stok hareket API aktarımı başarısız',
        timestamp: new Date().toISOString(),
      };
    }
  }
}
