"use client";
import React, { useEffect, useState } from "react";
import ProtectedPage from "../components/layout/ProtectedPage";
import PageContainer from "../components/layout/PageContainer";
import Sales from "./Sales";
import axios from "@/app/utils/customAxios";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  Loader2,
  Database,
  FileText,
  Package,
  Calendar,
  RefreshCw,
  RotateCcw,
} from "lucide-react";

const SalesPage = () => {
  const [sales, setSales] = useState<any[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  // Refunds state
  const [refunds, setRefunds] = useState<any[]>([]);
  const [refundsPage, setRefundsPage] = useState(1);
  const [refundsTotalPages, setRefundsTotalPages] = useState(1);
  const [refundsLoading, setRefundsLoading] = useState(false);
  // Active list tab
  const [activeList, setActiveList] = useState<"sales" | "refunds">("sales");
  const [fisLoading, setFisLoading] = useState(false);
  const [fisResult, setFisResult] = useState("");
  const [cariHareketLoading, setCariHareketLoading] = useState(false);
  const [cariHareketResult, setCariHareketResult] = useState("");
  const [stokHareketLoading, setStokHareketLoading] = useState(false);
  const [stokHareketResult, setStokHareketResult] = useState("");
  const [iadeAktarLoading, setIadeAktarLoading] = useState(false);
  const [iadeAktarResult, setIadeAktarResult] = useState("");

  const [tediyeAktarLoading, setTediyeAktarLoading] = useState(false);
  const [tediyeAktarResult, setTediyeAktarResult] = useState("");

  const [tahsilatAktarLoading, setTahsilatAktarLoading] = useState(false);
  const [tahsilatAktarResult, setTahsilatAktarResult] = useState("");

  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");

  const fisAktar = async () => {
    setFisLoading(true);
    setFisResult("");
    console.log("[FRONTEND] Muhasebe Aktar işlemi başlatıldı");

    try {
      const res: any = await axios.get("/muhasebe/ok");
      console.log("[FRONTEND] Muhasebe API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 5000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setFisResult(res.data.message || "Fiş aktarımı başarılı!");
          console.log("[FRONTEND] Muhasebe aktarımı başarılı:", res.data);
        } else {
          setFisResult(res.data.error || "Muhasebe sistemi hatası");
          console.error("[FRONTEND] Muhasebe sistemi hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setFisResult(errorMessage);
      console.error("[FRONTEND] Muhasebe Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setFisLoading(false);
    }
  };

  const cariHareketAktar = async () => {
    setCariHareketLoading(true);
    setCariHareketResult("");
    console.log("[FRONTEND] Cari Hesap Hareket aktarımı başlatıldı");

    try {
      const res: any = await axios.post(
        "/muhasebe/CariHesapHareketlerInsertDefault"
      );
      console.log("[FRONTEND] Cari Hesap API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 5000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setCariHareketResult(
            res.data.message || "Cari Hesap Hareketi aktarımı başarılı!"
          );
          console.log("[FRONTEND] Cari hesap aktarımı başarılı:", {
            processedCount: res.data.processedCount,
            message: res.data.message,
          });
        } else {
          setCariHareketResult(res.data.error || "Cari hesap aktarım hatası");
          console.error("[FRONTEND] Cari hesap aktarım hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setCariHareketResult(errorMessage);
      console.error("[FRONTEND] Cari Hesap Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setCariHareketLoading(false);
    }
  };

  const stokHareketAktar = async () => {
    setStokHareketLoading(true);
    setStokHareketResult("");
    console.log("[FRONTEND] Stok Hareket aktarımı başlatıldı");

    try {
      // Default değerler ile body oluşturma
      const stokHareketData = {
        cariKod: "DEFAULT001", // Varsayılan cari kodu
        stokKod: "STK001", // Varsayılan stok kodu
        miktar: 1, // Varsayılan miktar
        birimFiyat: 100, // Varsayılan birim fiyat
        subeKodu: 1, // Varsayılan şube kodu
        islemTipi: 1, // Varsayılan işlem tipi: Satış
        aciklama: "Varsayılan stok hareket kaydı", // Varsayılan açıklama
        kdvOrani: 20, // Varsayılan KDV oranı
        belgeNo: "BLG" + new Date().getTime(), // Dinamik belge numarası
      };

      console.log(
        "[FRONTEND] Stok hareket verisi gönderiliyor:",
        stokHareketData
      );

      const res: any = await axios.post(
        "/satis/stokHareketInsert",
        stokHareketData // Body verisi olarak eklendi
      );

      console.log("[FRONTEND] Stok Hareket API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 5000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setStokHareketResult(
            res.data.message || "Stok Hareketi aktarımı başarılı!"
          );
          console.log("[FRONTEND] Stok hareket aktarımı başarılı:", {
            totalProcessed: res.data.totalProcessed,
            successCount: res.data.successCount,
            errorCount: res.data.errorCount,
          });
        } else {
          setStokHareketResult(res.data.error || "Stok hareket aktarım hatası");
          console.error("[FRONTEND] Stok hareket aktarım hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setStokHareketResult(errorMessage);
      console.error("[FRONTEND] Stok Hareket Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setStokHareketLoading(false);
    }
  };

  const iadeAktar = async () => {
    setIadeAktarLoading(true);
    setIadeAktarResult("");
    console.log("[FRONTEND] İade aktarımı başlatıldı");

    try {
      const res: any = await axios.get("/giderpusulasi/create");
      console.log("[FRONTEND] İade API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 3000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setIadeAktarResult(res.data.message || "İade aktarımı başarılı!");
          console.log("[FRONTEND] İade aktarımı başarılı:", {
            refundsCount: res.data.data?.refundsCount,
            itemsCount: res.data.data?.itemsCount,
            message: res.data.message,
          });
        } else {
          setIadeAktarResult(res.data.error || "İade aktarım hatası");
          console.error("[FRONTEND] İade aktarım hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setIadeAktarResult(errorMessage);
      console.error("[FRONTEND] İade Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setIadeAktarLoading(false);
    }
  };

  const tahsilatAktar = async () => {
    setTahsilatAktarLoading(true);
    setTahsilatAktarResult("");
    console.log("[FRONTEND] Tahsilat aktarımı başlatıldı");

    try {
      const res: any = await axios.post("/tahsilat/create");
      console.log("[FRONTEND] Tahsilayt API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 3000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setTahsilatAktarResult(
            res.data.message || "Tahsilat aktarımı başarılı!"
          );
          console.log("[FRONTEND] Tahsilat aktarımı başarılı:", {
            refundsCount: res.data.data?.refundsCount,
            itemsCount: res.data.data?.itemsCount,
            message: res.data.message,
          });
        } else {
          setTahsilatAktarResult(res.data.error || "Tahsilat aktarım hatası");
          console.error("[FRONTEND] Tahsilat aktarım hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setTahsilatAktarResult(errorMessage);
      console.error("[FRONTEND] Tahsilat Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setTahsilatAktarLoading(false);
    }
  };

  const tediyeAktar = async () => {
    setTediyeAktarLoading(true);
    setTediyeAktarResult("");
    console.log("[FRONTEND] Tediye aktarımı başlatıldı");

    try {
      const res: any = await axios.post("/tediye/create");
      console.log("[FRONTEND] Tediye API yanıtı:", res.data);

      await new Promise((resolve) => setTimeout(resolve, 3000));

      if (res.status === 200 && res?.data) {
        if (res.data.success) {
          setTediyeAktarResult(res.data.message || "Tediye aktarımı başarılı!");
          console.log("[FRONTEND] Tediye aktarımı başarılı:", {
            refundsCount: res.data.data?.refundsCount,
            itemsCount: res.data.data?.itemsCount,
            message: res.data.message,
          });
        } else {
          setTediyeAktarResult(res.data.error || "Tahsilat aktarım hatası");
          console.error("[FRONTEND] Tediye aktarım hatası:", res.data);
        }
      }
    } catch (error: any) {
      const errorMessage =
        error.response?.data?.error ||
        error.message ||
        "Bir hata oluştu. Tekrar deneyin.";
      setTediyeAktarResult(errorMessage);
      console.error("[FRONTEND] Tediye Aktar hatası:", {
        error: error.message,
        response: error.response?.data,
        status: error.response?.status,
      });
    } finally {
      setTediyeAktarLoading(false);
    }
  };

  useEffect(() => {
    const fetchSales = async () => {
      try {
        setIsLoading(true);
        let url = `/sales?page=${currentPage}&limit=10`;
        if (startDate) url += `&startDate=${startDate}`;
        if (endDate) url += `&endDate=${endDate}`;
        const response = await axios.get(url);
        setSales(response.data.data);
        setTotalPages(response.data.totalPages);
      } catch (error) {
        console.error("Error fetching sales:", error);
      } finally {
        setIsLoading(false);
      }
    };

    const fetchRefunds = async () => {
      try {
        setRefundsLoading(true);
        let url = `/refunds/v2?page=${refundsPage}&limit=10`;
        if (startDate) url += `&startDate=${startDate}`;
        if (endDate) url += `&endDate=${endDate}`;
        const response = await axios.get(url);
        setRefunds(response.data.data);
        setRefundsTotalPages(response.data.totalPages);
      } catch (error) {
        console.error("Error fetching refunds:", error);
      } finally {
        setRefundsLoading(false);
      }
    };

    if (activeList === "sales") fetchSales();
    else fetchRefunds();
  }, [activeList, currentPage, refundsPage, startDate, endDate]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleRefundsPageChange = (page: number) => {
    setRefundsPage(page);
  };

  return (
    <ProtectedPage>
      <div className="w-full h-full flex flex-col items-center px-4 sm:px-10">
        <PageContainer
          pageHeader={"Satışlar"}
          title="Satışlar"
          description="Yapılan Satışları Listeleme, Detayları Görüntüleme"
        >
          {/* Tarih Filtreleme Kartı */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Tarih Filtreleme
              </CardTitle>
              <CardDescription>
                Belirli tarih aralığındaki satışları görüntülemek için filtre
                uygulayın
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap items-end gap-4">
                <div className="flex flex-col min-w-[200px]">
                  <Label
                    htmlFor="startDate"
                    className="text-sm font-medium mb-2"
                  >
                    Başlangıç Tarihi
                  </Label>
                  <Input
                    id="startDate"
                    type="datetime-local"
                    value={startDate}
                    onChange={(e) => setStartDate(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex flex-col min-w-[200px]">
                  <Label htmlFor="endDate" className="text-sm font-medium mb-2">
                    Bitiş Tarihi
                  </Label>
                  <Input
                    id="endDate"
                    type="datetime-local"
                    value={endDate}
                    onChange={(e) => setEndDate(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setStartDate("");
                    setEndDate("");
                    setCurrentPage(1);
                  }}
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Temizle
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Muhasebe Aktarım İşlemleri */}
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Muhasebe Aktarım İşlemleri
              </CardTitle>
              <CardDescription>
                Satış verilerini muhasebe sistemine aktarmak için kullanın
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Separator />

              <Separator />

              {/* Stok Hareketleri */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 p-4 border border-blue-200 bg-blue-50 rounded-lg">
                <Button
                  variant="outline"
                  onClick={stokHareketAktar}
                  disabled={stokHareketLoading}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100 flex items-center gap-2 min-w-[180px]"
                >
                  {stokHareketLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Package className="h-4 w-4" />
                  )}
                  Stok Hareket Aktar
                </Button>
                {stokHareketResult && (
                  <div className="flex items-center gap-2 text-blue-700 bg-blue-100 px-3 py-1 rounded-md">
                    <span className="text-sm font-medium">
                      {stokHareketResult}
                    </span>
                  </div>
                )}
              </div>

              <Separator />

              {/* İade Aktar */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 p-4 border border-purple-200 bg-purple-50 rounded-lg">
                <Button
                  variant="outline"
                  onClick={iadeAktar}
                  disabled={iadeAktarLoading}
                  className="border-purple-300 text-purple-700 hover:bg-purple-100 flex items-center gap-2 min-w-[180px]"
                >
                  {iadeAktarLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RotateCcw className="h-4 w-4" />
                  )}
                  İade Aktar
                </Button>
                {iadeAktarResult && (
                  <div className="flex items-center gap-2 text-purple-700 bg-purple-100 px-3 py-1 rounded-md">
                    <span className="text-sm font-medium">
                      {iadeAktarResult}
                    </span>
                  </div>
                )}
              </div>

              {/* Tediye Aktar */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 p-4 border border-purple-200 bg-purple-50 rounded-lg">
                <Button
                  variant="outline"
                  onClick={tediyeAktar}
                  disabled={tediyeAktarLoading}
                  className="border-purple-300 text-cyan-700 hover:bg-purple-100 flex items-center gap-2 min-w-[180px]"
                >
                  {tediyeAktarLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RotateCcw className="h-4 w-4" />
                  )}
                  Tediye Aktar
                </Button>
                {tediyeAktarResult && (
                  <div className="flex items-center gap-2 text-cyan-700 bg-purple-100 px-3 py-1 rounded-md">
                    <span className="text-sm font-medium">
                      {tediyeAktarResult}
                    </span>
                  </div>
                )}
              </div>

              {/* Tahsilat Aktar */}
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 p-4 border border-purple-200 bg-purple-50 rounded-lg">
                <Button
                  variant="outline"
                  onClick={tahsilatAktar}
                  disabled={tahsilatAktarLoading}
                  className="border-purple-300 text-purple-700 hover:bg-purple-100 flex items-center gap-2 min-w-[180px]"
                >
                  {tahsilatAktarLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <RotateCcw className="h-4 w-4" />
                  )}
                  Tahsilat Aktar
                </Button>
                {tahsilatAktarResult && (
                  <div className="flex items-center gap-2 text-purple-700 bg-purple-100 px-3 py-1 rounded-md">
                    <span className="text-sm font-medium">
                      {tahsilatAktarResult}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          <Sales
            sales={sales}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            isLoading={isLoading}
            refunds={refunds}
            refundsCurrentPage={refundsPage}
            refundsTotalPages={refundsTotalPages}
            onRefundsPageChange={handleRefundsPageChange}
            refundsLoading={refundsLoading}
            activeList={activeList}
            onActiveListChange={(t) => {
              setActiveList(t);
              // reset page on switch for clarity
              if (t === "sales") setCurrentPage(1);
              else setRefundsPage(1);
            }}
          />
        </PageContainer>
      </div>
    </ProtectedPage>
  );
};

export default SalesPage;
