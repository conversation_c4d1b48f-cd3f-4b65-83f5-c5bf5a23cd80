using System.ComponentModel.DataAnnotations;

namespace dotnettest.Models
{
    public class TediyeModelRequest
    {
        [Required]
        public TediyeMikroData Mikro { get; set; } = new TediyeMikroData();
    }

    public class TediyeMikroData
    {
        [Required]
        public string FirmaKodu { get; set; } = "";

        [Required]
        public int CalismaYili { get; set; }

        [Required]
        public string KullaniciKodu { get; set; } = "";

        [Required]
        public string Sifre { get; set; } = "";

        public int FirmaNo { get; set; }

        public int SubeNo { get; set; }

        [Required]
        public string ApiKey { get; set; } = "";

        [Required]
        public List<TediyeEvrak> evraklar { get; set; } = new List<TediyeEvrak>();
    }

    public class TediyeEvrak
    {
        public List<TediyeEvrakAciklama> evrak_aciklamalari { get; set; } = new List<TediyeEvrakAciklama>();

        [Required]
        public List<TediyeSatir> satirlar { get; set; } = new List<TediyeSatir>();
    }

    public class TediyeEvrakAciklama
    {
        [Required]
        public string aciklama { get; set; } = "";
    }

    public class TediyeSatir
    {
        [Required]
        public string cha_tarihi { get; set; } = "";

        [Required]
        public int cha_tip { get; set; }

        [Required]
        public int cha_cinsi { get; set; }

        [Required]
        public int cha_normal_Iade { get; set; }

        [Required]
        public int cha_evrak_tip { get; set; }

        [Required]
        public string cha_evrakno_seri { get; set; } = "";

        [Required]
        public int cha_cari_cins { get; set; }

        [Required]
        public string cha_kod { get; set; } = "";

        public string? cha_d_kurtar { get; set; }

        [Required]
        public int cha_d_cins { get; set; }

        [Required]
        public decimal cha_d_kur { get; set; }

        [Required]
        public string cha_srmrkkodu { get; set; } = "";

        public string cha_projekodu { get; set; } = "";

        [Required]
        public int cha_kasa_hizmet { get; set; }

        [Required]
        public string cha_kasa_hizkod { get; set; } = "";

        [Required]
        public string cha_vade { get; set; } = "";

        [Required]
        public string cha_meblag { get; set; } = "";

        [Required]
        public string cha_aciklama { get; set; } = "";

        public List<TediyeUserTablo> user_tablo { get; set; } = new List<TediyeUserTablo>();
    }

    public class TediyeUserTablo
    {
        public string TransactionReferenceId { get; set; } = "";
    }
}
