using Microsoft.AspNetCore.Mvc;
using dotnettest.Models;
using dotnettest.Services;
using System.Security.Cryptography;
using System.Text;

namespace dotnettest.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class MikroApiController : ControllerBase
    {
        private readonly IMikroApiService _apiService;
        private readonly ILogger<MikroApiController> _logger;
        private readonly IConfiguration _configuration;

        public MikroApiController(IMikroApiService apiService, ILogger<MikroApiController> logger, IConfiguration configuration)
        {
            _apiService = apiService;
            _logger = logger;
            _configuration = configuration;
        }

        [HttpGet]
        public IActionResult Index()
        {
            _logger.LogInformation("GET /api/MikroApi - Index endpoint çağrıldı");
            return Ok("Mikro API İşlemleri");
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login()
        {
            _logger.LogInformation("POST /api/MikroApi/login - Login isteği başlatıldı. IP: {RemoteIpAddress}",
                HttpContext.Connection.RemoteIpAddress);
            _logger.LogInformation("API Login isteği başlatıldı");

            try
            {
                var result = await _apiService.LoginAsync();
                _logger.LogInformation("API Login isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "API Login isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }

        [HttpGet("stok-listesi")]
        public IActionResult GetStokListesiTemplate()
        {
            _logger.LogInformation("GET /api/MikroApi/stok-listesi - Template isteği. IP: {RemoteIpAddress}",
                HttpContext.Connection.RemoteIpAddress);

            var model = new StokListesiRequestDto
            {
                StokKod = "",
                TarihTipi = 2,
                IlkTarih = "2024-01-01",
                SonTarih = "2025-07-01",
                Sort = "-sto_kod",
                Size = "5",
                Index = 0
            };
            return Ok(model);
        }

        [HttpPost("stok-listesi")]
        public async Task<IActionResult> StokListesi([FromBody] StokListesiRequestDto model)
        {
            _logger.LogInformation("POST /api/MikroApi/stok-listesi - Stok Listesi isteği. IP: {RemoteIpAddress}, StokKod: {StokKod}, Tarih: {IlkTarih}-{SonTarih}",
                HttpContext.Connection.RemoteIpAddress, model?.StokKod ?? "null", model?.IlkTarih ?? "null", model?.SonTarih ?? "null");
            _logger.LogInformation("Stok Listesi isteği başlatıldı");

            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            try
            {
                var result = await _apiService.GetStokListesiAsync(model);
                _logger.LogInformation("Stok Listesi isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Stok Listesi isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }

        [HttpGet("alim-satim-evragi2")]
        public async Task<IActionResult> GetAlimSatimEvragiTemplate()
        {
            _logger.LogInformation("GET /api/MikroApi/alim-satim-evragi - Template ve otomatik test isteği. IP: {RemoteIpAddress}",
                HttpContext.Connection.RemoteIpAddress);

            // Günün tarihini al (dd.MM.yyyy formatında)
            string todayDate = DateTime.Now.ToString("yyyy-MM-dd");

            // LoginSifre'yi al
            string loginSifre = _configuration["MikroApi:LoginSifre"] ?? "159753";

            // Tarih + boşluk + şifre kombinasyonunu oluştur
            string combinedString = $"{todayDate} {loginSifre}";
            // MD5 hash oluştur
            string md5Hash = "";
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.ASCII.GetBytes(combinedString);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                // Hash'i hex string'e dönüştür
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }
                md5Hash = sb.ToString();
            }

            var sampleData = new AlimSatimEvragiKaydetV2Request
            {
                Mikro = new MikroData
                {
                    FirmaKodu = "SOPHIGET",//_configuration["MikroApi:FirmaKodu"] ?? "SOPHIGET",
                    CalismaYili = "2025",// _configuration["MikroApi:CalismaYili"] ?? "2025",
                    KullaniciKodu = "API",// _configuration["MikroApi:KullaniciKodu"] ?? "API",
                    Sifre = "65e24175ca9be049aadad86f4560db26",//md5Hash,
                    //ApiKey = _configuration["MikroApi:ApiSifre"] ?? "+l0/ibuYEszgdtALHaUByLuvUx6CCdpHbDEPwzh+5uP2rH9jVnESFRk1MPbcOc7F17N0jdfdrXtgtzPgEkSZqK7txJhjXSGlup95MdUqNIA=",
                    ApiKey = "+l0/ibuYEszgdtALHaUByLuvUx6CCdpHbDEPwzh+5uP2rH9jVnESFRk1MPbcOc7F17N0jdfdrXtgtzPgEkSZqK7txJhjXSGlup95MdUqNIA=",
                    FirmaNo = 0,
                    subeno = 0,
                    evraklar = new List<Evrak>
                    {
                        new Evrak
                        {
                            cha_tarihi = "28.07.2025",
                            cha_tip = 0,
                            cha_cinsi = 7,
                            cha_normal_Iade = 0,
                            cha_evrak_tip = 63,
                            cha_evrakno_seri = "PSF103",
                            cha_evrakno_sira = 4,//sistemdeen son değerin art 1 değeri al
                            cha_cari_cins = 0,
                            cha_kod = "0010087612",
                            cha_ciro_cari_kodu = "",
                            cha_d_kur = 1,
                            cha_srmrkkodu = "101",
                            cha_karsid_kur = 0,
                            cha_miktari = "0",
                            cha_meblag = 100.00M,
                            cha_aratoplam = 100.00M,
                            cha_ft_iskonto1 = 0.00M,//promasyondan gelen indirim tutarını yaz
                            cha_ft_iskonto2 = 0.00M,
                            cha_ft_iskonto3 = 0.00M,
                            cha_ft_iskonto4 = 0.00M,
                            cha_ft_iskonto5 = 0.00M,
                            cha_ft_iskonto6 = 0.00M,
                            cha_vergipntr = 0,
                            cha_pos_hareketi = 1,
                            cha_adres_no = 1,
                            cha_vergi1 = 0.00M,
                            cha_vergi2 = 0.00M,
                            cha_vergi3 = 0.00M,
                            cha_vergi4 = 0.00M,
                            cha_vergi5 = 0.00M,
                            cha_vergi6 = 0.00M,
                            cha_vergi7 = 0.00M,
                            cha_vergi8 = 0.00M,
                            cha_vergi9 = 0.00M,
                            cha_vergi10 = 0.00M,
                            cha_aciklama = "",
                            cha_belge_no = "",
                            cha_belge_tarih = "",
                            evrak_aciklamalari = new List<EvrakAciklama>
                            {
                                new EvrakAciklama { aciklama = "Test açıklama 1" }
                            },
                            user_tablo = new List<UserTablo>
                            {
                                new UserTablo
                                {
                                    SubDealer = "",
                                    CreditRelationCustomer = "",
                                    CreditReferenceNumber = "",
                                    WebSupportCustomer = "",
                                    RegisteredEMailAccount = "",
                                    WebSupportStartDate = "",
                                    RentalCustomer = "",
                                    DetailDescription1 = "Detay açıklama 1",
                                    DetailDescription2 = "Detay açıklama 2"
                                }
                            },
                            detay = new List<Detay>
                            {
                                new Detay
                                {
                                    sth_tarih = "21.07.2025",
                                    sth_tip = 1,
                                    sth_cins = 1,
                                    sth_normal_iade = 0,
                                    sth_evraktip = "4",
                                    sth_evrakno_seri = "PSF103",
                                    sth_evrakno_sira = 4,// aynısı olacak                            cha_evrakno_sira = 1,//sistemdeen son değerin art 1 değeri al
                                    sth_belge_no = "",
                                    sth_belge_tarih = "",
                                    sth_stok_kod = "031720",
                                    sth_pos_satis = 0,
                                    sth_cari_cinsi = "0",
                                    sth_cari_kodu = "0010087612",
                                    sth_har_doviz_kuru = 1.0M,
                                    sth_stok_doviz_kuru = 1.0M,
                                    sth_miktar = 1,
                                    sth_birim_pntr = 1,
                                    sth_tutar = 25,
                                    sth_iskonto1 = 0,
                                    sth_iskonto2 = 0,
                                    sth_iskonto3 = 0,
                                    sth_iskonto4 = 0,
                                    sth_iskonto5 = 0,
                                    sth_iskonto6 = 0,
                                    sth_vergi_pntr = 1,
                                    sth_vergi = 0,
                                    sth_aciklama = "",
                                    sth_giris_depo_no = "101",
                                    sth_cikis_depo_no = "101",
                                    sth_cari_srm_merkezi = "101",
                                    sth_stok_srm_merkezi = "101",
                                    sth_vergisiz_fl = false,
                                    sth_adres_no = 0,
                                    sth_fiyat_liste_no = 0,
                                    sth_satis_fiyat_doviz_kuru = 1.0M
                                }
                            }
                        }
                    }
                }
            };

            _logger.LogInformation("AlimSatimEvragiKaydetV2 test verileri ile otomatik çağrı başlatıldı");

            try
            {
                _logger.LogInformation("Test verileri hazırlandı: FirmaKodu={FirmaKodu}, CalismaYili={CalismaYili}, KullaniciKodu={KullaniciKodu}",
                    sampleData.Mikro.FirmaKodu, sampleData.Mikro.CalismaYili, sampleData.Mikro.KullaniciKodu);

                var result = await _apiService.AlimSatimEvragiKaydetV2Async(sampleData);

                _logger.LogInformation("AlimSatimEvragiKaydetV2 test çağrısı tamamlandı. Success: {Success}, StatusCode: {StatusCode}, Message: {Message}",
                    result.Success, result.StatusCode, result.Message);

                return Ok(new
                {
                    Success = true,
                    Message = "Test başarıyla çalıştırıldı",
                    TestData = new
                    {
                        FirmaKodu = sampleData.Mikro.FirmaKodu,
                        CalismaYili = sampleData.Mikro.CalismaYili,
                        KullaniciKodu = sampleData.Mikro.KullaniciKodu,
                        EvrakSayisi = sampleData.Mikro.evraklar.Count,
                        DetaySayisi = sampleData.Mikro.evraklar.FirstOrDefault()?.detay?.Count ?? 0
                    },
                    LoginBilgileri = new
                    {
                        FirmaKodu = sampleData.Mikro.FirmaKodu,
                        CalismaYili = sampleData.Mikro.CalismaYili,
                        KullaniciKodu = sampleData.Mikro.KullaniciKodu,
                        Sifre = sampleData.Mikro.Sifre,
                        SifreAciklama = $"MD5({combinedString})",
                        ApiKey = sampleData.Mikro.ApiKey,
                        FirmaNo = sampleData.Mikro.FirmaNo,
                        SubeNo = sampleData.Mikro.subeno
                    },
                    ApiResult = result
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AlimSatimEvragiKaydetV2 test çağrısı sırasında hata oluştu. Hata: {ErrorMessage}", ex.Message);

                return BadRequest(new
                {
                    Success = false,
                    Message = "Test sırasında hata oluştu",
                    Error = ex.Message,
                    ErrorType = ex.GetType().Name,
                    TestData = new
                    {
                        FirmaKodu = sampleData.Mikro.FirmaKodu,
                        CalismaYili = sampleData.Mikro.CalismaYili,
                        KullaniciKodu = sampleData.Mikro.KullaniciKodu
                    },
                    LoginBilgileri = new
                    {
                        FirmaKodu = sampleData.Mikro.FirmaKodu,
                        CalismaYili = sampleData.Mikro.CalismaYili,
                        KullaniciKodu = sampleData.Mikro.KullaniciKodu,
                        Sifre = sampleData.Mikro.Sifre,
                        SifreAciklama = $"MD5({combinedString})",
                        ApiKey = sampleData.Mikro.ApiKey,
                        FirmaNo = sampleData.Mikro.FirmaNo,
                        SubeNo = sampleData.Mikro.subeno
                    }
                });
            }
        }

        [HttpPost("alim-satim-evragi")]
        public async Task<IActionResult> AlimSatimEvragi([FromBody] AlimSatimEvragiKaydetV2Request model)
        {
            _logger.LogInformation("POST /api/MikroApi/alim-satim-evragi - AlimSatimEvragi isteği. IP: {RemoteIpAddress}, FirmaKodu: {FirmaKodu}, EvrakSayisi: {EvrakSayisi}",
                HttpContext.Connection.RemoteIpAddress,
                model?.Mikro?.FirmaKodu ?? "null",
                model?.Mikro?.evraklar?.Count ?? 0);

            // POST ile gönderilen veriyi console'a yazdır
            _logger.LogInformation("📤 POST Data Buraya Geldi: {@PostData}", model);
            Console.WriteLine($"📤 POST Data omer: {System.Text.Json.JsonSerializer.Serialize(model, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}");

            _logger.LogInformation("AlimSatimEvragiKaydetV2 isteği başlatıldı");

            try
            {
                var result = await _apiService.AlimSatimEvragiKaydetV2Async(model);
                _logger.LogInformation("AlimSatimEvragiKaydetV2 isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "AlimSatimEvragiKaydetV2 isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }

        [HttpPost("giderpusulasiCreate")]
        public async Task<IActionResult> GiderPusulasiCreate([FromBody] AlimSatimEvragiKaydetV2Request model)
        {
            _logger.LogInformation("POST /api/MikroApi/giderpusulasiCreate - GiderPusulasi isteği. IP: {RemoteIpAddress}, EvrakSayisi: {EvrakSayisi}",
                HttpContext.Connection.RemoteIpAddress,
                model?.Mikro?.evraklar?.Count ?? 0);

            // POST ile gönderilen veriyi console'a yazdır
            _logger.LogInformation("📤 Gider Pusulası POST Data: {@PostData}", model);
            Console.WriteLine($"📤 Gider Pusulası POST Data: {System.Text.Json.JsonSerializer.Serialize(model, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}");

            // API şifresi oluştur (tahsilatCreate'daki gibi)
            if (model?.Mikro != null)
            {
                // Günün tarihini al (yyyy-MM-dd formatında)
                string todayDate = DateTime.Now.ToString("yyyy-MM-dd");

                // LoginSifre'yi al
                string loginSifre = _configuration["MikroApi:LoginSifre"] ?? "159753";

                // Tarih + boşluk + şifre kombinasyonunu oluştur
                string combinedString = $"{todayDate} {loginSifre}";

                // MD5 hash oluştur
                string md5Hash = "";
                using (MD5 md5 = MD5.Create())
                {
                    byte[] inputBytes = Encoding.ASCII.GetBytes(combinedString);
                    byte[] hashBytes = md5.ComputeHash(inputBytes);

                    // Hash'i hex string'e dönüştür
                    StringBuilder sb = new StringBuilder();
                    for (int i = 0; i < hashBytes.Length; i++)
                    {
                        sb.Append(hashBytes[i].ToString("x2"));
                    }
                    md5Hash = sb.ToString();
                }

                // Model'in Sifre alanını güncelle
                model.Mikro.Sifre = md5Hash;

                _logger.LogInformation("API şifresi güncellendi. Tarih: {Date}, Hash: {Hash}", todayDate, md5Hash);
            }

            _logger.LogInformation("GiderPusulasiCreate isteği başlatıldı");

            try
            {
                var result = await _apiService.GiderPusulasiCreateAsync(model);
                _logger.LogInformation("GiderPusulasiCreate isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "GiderPusulasiCreate isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }

        [HttpPost("tahsilatCreate")]
        public async Task<IActionResult> TahsilatCreate([FromBody] TahsilatModelRequest model)
        {

            _logger.LogInformation("POST /api/MikroApi/tahsilatCreate - Tahsilat isteği. IP: {RemoteIpAddress}, EvrakSayisi: {EvrakSayisi}",
                HttpContext.Connection.RemoteIpAddress,
                model?.Mikro?.evraklar?.Count ?? 0);

            // POST ile gönderilen veriyi console'a yazdır
            _logger.LogInformation("📤 Tahsilat POST Data: {@PostData}", model);
            Console.WriteLine($"📤 Tahsilat POST Data: {System.Text.Json.JsonSerializer.Serialize(model, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}");

            _logger.LogInformation("TahsilatCreate isteği başlatıldı");

            try
            {
                var result = await _apiService.TahsilatCreateAsync(model);
                _logger.LogInformation("TahsilatCreate isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TahsilatCreate isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }
     
     
     
        [HttpPost("tediyeCreate")]
        public async Task<IActionResult> TediyeCreate([FromBody] TediyeModelRequest model)
        {

            _logger.LogInformation("POST /api/MikroApi/tediyeCreate - Tediye isteği. IP: {RemoteIpAddress}, EvrakSayisi: {EvrakSayisi}",
                HttpContext.Connection.RemoteIpAddress,
                model?.Mikro?.evraklar?.Count ?? 0);

            // POST ile gönderilen veriyi console'a yazdır
            _logger.LogInformation("📤 Tediye POST Data: {@PostData}", model);
            Console.WriteLine($"📤 Tediye POST Data: {System.Text.Json.JsonSerializer.Serialize(model, new System.Text.Json.JsonSerializerOptions { WriteIndented = true })}");

            _logger.LogInformation("TediyeCreate isteği başlatıldı");

           

            try
            {
                var result = await _apiService.TediyeCreateAsync(model);
                _logger.LogInformation("TediyeCreate isteği tamamlandı. Success: {Success}", result.Success);

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "TediyeCreate isteği sırasında hata oluştu");
                return BadRequest($"API isteği sırasında hata oluştu: {ex.Message}");
            }
        }



    }
}
