using dotnettest.Models;

namespace dotnettest.Services
{
    public interface IMikroApiService
    {
        Task<ApiResponse<string>> LoginAsync();
        Task<ApiResponse<string>> GetStokListesiAsync();
        Task<ApiResponse<string>> GetStokListesiAsync(StokListesiRequestDto request);
        Task<ApiResponse<string>> AlimSatimEvragiKaydetV2Async(AlimSatimEvragiKaydetV2Request request);
        Task<ApiResponse<string>> GiderPusulasiCreateAsync(AlimSatimEvragiKaydetV2Request request);
        Task<ApiResponse<string>> TahsilatCreateAsync(TahsilatModelRequest request);
        Task<ApiResponse<string>> TediyeCreateAsync(TediyeModelRequest request);


        
    }
}
