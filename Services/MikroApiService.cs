using System.Text;
using System.Text.Json;
using dotnettest.Models;

namespace dotnettest.Services
{
    public class MikroApiService : IMikroApiService
    {
        private readonly HttpClient _httpClient;
        private readonly MikroApiSettings _settings;
        private readonly ILogger<MikroApiService> _logger;

        public MikroApiService(
            HttpClient httpClient,
            IConfiguration configuration,
            ILogger<MikroApiService> logger)
        {
            _httpClient = httpClient;
            _settings = configuration.GetSection("MikroApi").Get<MikroApiSettings>() ?? new MikroApiSettings();
            _logger = logger;

            _httpClient.Timeout = TimeSpan.FromSeconds(_settings.TimeoutSeconds);
            _httpClient.BaseAddress = new Uri(_settings.BaseUrl);
        }

        private string GenerateApiPassword()
        {
            var today = DateTime.Today.ToString("yyyy-MM-dd");
            var passwordString = $"{today} {_settings.LoginSifre}";

            return "9fce5c5935a265b4e6f54754c25158fd";
        }

        public async Task<ApiResponse<string>> LoginAsync()
        {
            var loginRequest = new ApiLoginRequest
            {
                FirmaKodu = _settings.FirmaKodu,
                CalismaYili = _settings.CalismaYili,
                ApiKey = "",
                KullaniciKodu = _settings.KullaniciKodu,
                Sifre = _settings.LoginSifre,
                FirmaNo = 0,
                SubeNo = 0
            };

            return await SendPostRequestAsync("/Api/APIMethods/APILogin", loginRequest);
        }

        public async Task<ApiResponse<string>> GetStokListesiAsync()
        {
            var defaultRequest = new StokListesiRequestDto
            {
                StokKod = "",
                TarihTipi = 2,
                IlkTarih = "2024-01-01",
                SonTarih = "2025-07-01",
                Sort = "-sto_kod",
                Size = "5",
                Index = 0
            };

            return await GetStokListesiAsync(defaultRequest);
        }

        public async Task<ApiResponse<string>> GetStokListesiAsync(StokListesiRequestDto request)
        {
            var stokListesiRequest = new StokListesiRequest
            {
                Mikro = new MikroInfo
                {
                    FirmaKodu = _settings.FirmaKodu,
                    CalismaYili = _settings.CalismaYili,
                    KullaniciKodu = _settings.KullaniciKodu,
                    Sifre = GenerateApiPassword()
                },
                StokKod = request.StokKod,
                TarihTipi = request.TarihTipi,
                IlkTarih = request.IlkTarih,
                SonTarih = request.SonTarih,
                Sort = request.Sort,
                Size = request.Size,
                Index = request.Index
            };

            return await SendPostRequestAsync("/Api/APIMethods/StokListesiV2", stokListesiRequest);
        }

        public async Task<ApiResponse<string>> AlimSatimEvragiKaydetV2Async(AlimSatimEvragiKaydetV2Request request)
        {
            if (request?.Mikro == null)
            {
                _logger.LogInformation("AlimSatimEvragiKaydetV2 isteği için demo veriler kullanılıyor");
                request = GetDemoAlimSatimEvragiData();
            }
            else
            {
                if (string.IsNullOrEmpty(request.Mikro.FirmaKodu))
                    request.Mikro.FirmaKodu = _settings.FirmaKodu;
                if (string.IsNullOrEmpty(request.Mikro.CalismaYili))
                    request.Mikro.CalismaYili = _settings.CalismaYili;
                if (string.IsNullOrEmpty(request.Mikro.KullaniciKodu))
                    request.Mikro.KullaniciKodu = _settings.KullaniciKodu;
                if (string.IsNullOrEmpty(request.Mikro.Sifre))
                    request.Mikro.Sifre = "9fce5c5935a265b4e6f54754c25158fd";
            }

            _logger.LogInformation("Vereriler: {@Request}", request);

            return await SendPostRequestAsync("/Api/apiMethods/AlimSatimEvragiKaydetV2", request);
        }

        public async Task<ApiResponse<string>> GiderPusulasiCreateAsync(AlimSatimEvragiKaydetV2Request request)
        {
            // Gelen veriyi log'a yazdır
            var giderJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("Gider Pusulası Verileri: {GiderData}", giderJson);

            // Direkt AlimSatimEvragiKaydetV2 endpoint'ine gönder
            return await AlimSatimEvragiKaydetV2Async(request);
        }


        private AlimSatimEvragiKaydetV2Request GetDemoAlimSatimEvragiData()
        {
            return new AlimSatimEvragiKaydetV2Request
            {
                Mikro = new MikroData
                {
                    FirmaKodu = "MIKROFLY",
                    CalismaYili = "2023",
                    KullaniciKodu = "SRV",
                    Sifre = "9fce5c5935a265b4e6f54754c25158fd",
                    FirmaNo = 0,
                    subeno = 0,
                    evraklar = new List<Evrak>
                    {
                        new Evrak
                        {
                            cha_tarihi = "22.01.2024",
                            cha_tip = 0,
                            cha_cinsi = 6,
                            cha_normal_Iade = 0,
                            cha_evrak_tip = 63,
                            cha_evrakno_seri = "AS",
                            cha_evrakno_sira = 0,
                            cha_cari_cins = 0,
                            cha_kod = "CR01",
                            cha_ciro_cari_kodu = "CR01",
                            cha_d_kur = 1,
                            cha_srmrkkodu = "",
                            cha_karsid_kur = 0,
                            cha_miktari = "0",
                            cha_meblag = 0,
                            cha_aratoplam = 0,
                            cha_ft_iskonto1 = 0.0M,
                            cha_ft_iskonto2 = 0.0M,
                            cha_ft_iskonto3 = 0.0M,
                            cha_ft_iskonto4 = 0.0M,
                            cha_ft_iskonto5 = 0.0M,
                            cha_ft_iskonto6 = 0.0M,
                            cha_vergipntr = 0,
                            cha_pos_hareketi = 0,
                            cha_adres_no = 0,
                            cha_vergi1 = 0.0M,
                            cha_vergi2 = 0.0M,
                            cha_vergi3 = 0.0M,
                            cha_vergi4 = 0.0M,
                            cha_vergi5 = 0.0M,
                            cha_vergi6 = 0.0M,
                            cha_vergi7 = 0.0M,
                            cha_vergi8 = 0.0M,
                            cha_vergi9 = 0.0M,
                            cha_vergi10 = 0.0M,
                            cha_fatura_belge_turu = 0,
                            cha_diger_belge_adi = "",
                            cha_aciklama = "",
                            cha_belge_no = "",
                            cha_belge_tarih = ""
                        }
                    }
                }
            };
        }

        private async Task<ApiResponse<string>> SendPostRequestAsync<T>(string url, T requestData)
        {
            try
            {
                var json = JsonSerializer.Serialize(requestData);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _logger.LogDebug("API isteği gönderiliyor: {Url}", url);

                using var response = await _httpClient.PostAsync(url, content);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogDebug("API yanıtı alındı. StatusCode: {StatusCode}", response.StatusCode);

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogWarning("API hatası. StatusCode: {StatusCode}, Response: {Response}",
                        response.StatusCode, responseContent);
                }

                return new ApiResponse<string>
                {
                    Success = response.IsSuccessStatusCode,
                    StatusCode = (int)response.StatusCode,
                    Data = responseContent,
                    Message = response.IsSuccessStatusCode ? "İşlem başarılı" : $"API Hatası: {response.StatusCode}"
                };
            }
            catch (TaskCanceledException ex)
            {
                _logger.LogError(ex, "API isteği zaman aşımına uğradı: {Url}", url);
                return new ApiResponse<string>
                {
                    Success = false,
                    StatusCode = 408,
                    Message = "İstek zaman aşımına uğradı"
                };
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP isteği hatası: {Url}", url);
                return new ApiResponse<string>
                {
                    Success = false,
                    StatusCode = 500,
                    Message = $"HTTP isteği hatası: {ex.Message}"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Beklenmeyen hata: {Url}", url);
                return new ApiResponse<string>
                {
                    Success = false,
                    StatusCode = 500,
                    Message = $"Beklenmeyen hata: {ex.Message}"
                };
            }
        }

        public async Task<ApiResponse<string>> TahsilatCreateAsync(TahsilatModelRequest request)
        {
            // Gelen veriyi log'a yazdır
            var tahsilatJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("Tahsilat Verileri: {TahsilatData}", tahsilatJson);

            // TahsilatModelRequest'i tahsilat API'sine gönder
            return await SendPostRequestAsync("/Api/apiMethods/TahsilatTediyeKaydetV2", request);
        }


        public async Task<ApiResponse<string>> TediyeCreateAsync(TediyeModelRequest request)
        {
            // Gelen veriyi log'a yazdır
            var tahsilatJson = JsonSerializer.Serialize(request, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("Tediye Verileri: {TediyeData}", tahsilatJson);

            // TahsilatModelRequest'i tahsilat API'sine gönder
            return await SendPostRequestAsync("/Api/apiMethods/TahsilatTediyeKaydetV2", request);
        }

    }
}
